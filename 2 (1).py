import time
from media.sensor import *
from media.display import *
from media.media import *
import math


# ---------- 参数 ----------
LCD_W      = 800
LCD_H      = 480
A4_REAL_W  = 210.0        # mm
FOCAL_PIX  = 395.238        # 标定后填入
# --------------------------

'''
    校准过程:
    把 A4 纸 正面垂直对准摄像头，用尺子量出真实距离 d_cm（例如 30 cm）。
    运行程序，记下屏幕此时外框的像素宽度 w_px（串口或直接在屏幕上看）。
    用公式反推：
    FOCAL_LENGTH_PIX = (w_px * d_cm * 10) / A4_REAL_WIDTH_MM
    例：d_cm = 30 cm 时，屏幕测得 w_px = 215 px
    FOCAL_LENGTH_PIX = 215 * 30 * 10 / 210 ≈ 307
    把计算值写回代码里的 FOCAL_LENGTH_PIX，再验证其它距离即可。
    若结果仍偏差大，可重复 1~3 步取多次平均。
'''

sensor = Sensor(width=1280, height=960)
sensor.reset()
sensor.set_framesize(width=320, height=240)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init()
sensor.run()

clk = time.clock()

# -------------------------------------------------
# 工具函数
# -------------------------------------------------
def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55


def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho):
            return False
    return True


def dist_mm(p1, p2, current_pix_w):
    pix_dist = math.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
    return (pix_dist * A4_REAL_W) / current_pix_w


def clamp_roi(x, y, w, h, img_w, img_h):
    x = max(0, min(x, img_w - 1))
    y = max(0, min(y, img_h - 1))
    w = max(1, min(w, img_w - x))
    h = max(1, min(h, img_h - y))
    return (x, y, w, h)


def draw_poly(img, pts, color, thickness=2):
    n = len(pts)
    for i in range(n):
        x1, y1 = int(pts[i][0]), int(pts[i][1])
        x2, y2 = int(pts[(i+1) % n][0]), int(pts[(i+1) % n][1])
        img.draw_line(x1, y1, x2, y2, color=color, thickness=thickness)

# -------------------------------------------------
# 主循环
# -------------------------------------------------
while True:
    clk.tick()
    img = sensor.snapshot()

    rects = img.find_rects(threshold=10000)
    rects = sorted(rects, key=lambda r: r.w()*r.h(), reverse=True)

    outer = inner = None
    if len(rects) >= 2:
        cand = [r for r in rects[:2] if is_a4_like(r)]
        if len(cand) == 2:
            outer, inner = cand[0], cand[1]
            if not inside(inner, outer):
                outer = inner = None

    dist_mm_a4 = 0
    if outer and inner:
        img.draw_rectangle(outer.rect(), color=(255, 0, 0), thickness=2)
        for p in outer.corners():
            img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
        img.draw_rectangle(inner.rect(), color=(0, 255, 255), thickness=2)
        for p in inner.corners():
            img.draw_circle(p[0], p[1], 5, color=(0, 0, 255))

        pixel_width = outer.w()
        dist_mm_a4  = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 5, "A4:%.1f cm" % (dist_mm_a4/10),
                        color=(255, 255, 255), scale=2)
        pixel_width = outer.w()
        dist_mm_a4  = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 200, "w_px=%d" % pixel_width,
                        color=(255, 255, 255), scale=2)

        shrink = 5
        x0, y0, w0, h0 = inner.rect()
        x0, y0, w0, h0 = clamp_roi(x0 + shrink, y0 + shrink,
                                   w0 - 2*shrink, h0 - 2*shrink,
                                   img.width(), img.height())
        # ---- 圆形 ----
        circles = img.find_circles(threshold=3000,
                                   x_margin=10, y_margin=10, r_margin=10,
                                   r_min=2, r_max=100, r_step=2,
                                   roi=(x0, y0, w0, h0))
        has_circle = False
        for c in circles:
            has_circle = True
            dia_mm = 2 * c.r() * A4_REAL_W / pixel_width
            img.draw_circle(c.x(), c.y(), c.r(), color=(0, 255, 0), thickness=2)
            img.draw_string(int(c.x()-10), int(c.y()-10),
                                     "C:%.1f mm" % dia_mm,
                                     color=(0, 255, 0), scale=1)
            print("Circle dia = %.1f mm" % dia_mm)
        # ---- 三角形 & 四边形（用 find_blobs + 顶点逼近） ----
        if not has_circle:
            gray = img.to_grayscale(roi=(x0, y0, w0, h0))
            bw   = gray.binary([(0, 80)],invert=False)
            inner_area = w0 * h0
            MIN_AREA   = int(inner_area * 0.1)
            MAX_AREA   = int(inner_area * 0.7)
            center_x = w0 // 2
            center_y = h0 // 2
            blobs = sorted(
                        bw.find_blobs([(255, 255)],
                                      pixels_threshold=100,
                                      area_threshold=MIN_AREA,
                                      roi=(0, 0, w0, h0)),
                        key=lambda b: (b.cx() - center_x) ** 2 + (b.cy() - center_y) ** 2
                    )

            for blob in blobs:
                corners = blob.min_corners()
                corners = [(cx + x0, cy + y0) for (cx, cy) in corners]
                blob_area =  blob.pixels()
                bbox_area = 0.5 * abs(sum(corners[i][0] * corners[(i+1)%4][1] - corners[(i+1)%4][0] * corners[i][1] for i in range(4)))
                area_ratio = blob_area / bbox_area if bbox_area > 0 else 1
                if  ( area_ratio < 0.9):
                    # 三角形
                    a = dist_mm(corners[0], corners[1], pixel_width)
                    b = dist_mm(corners[1], corners[2], pixel_width)
                    c = dist_mm(corners[2], corners[0], pixel_width)
                    # draw_poly(img, corners, color=(255, 255, 0), thickness=2)
                    info = "T:%.1f/%.1f/%.1f mm" % (a, b, c)
                    img.draw_string(int(blob.cx()+x0), int(blob.cy()+y0), info,
                                    color=(255, 255, 0), scale=1)
                    print("Triangle sides:", a, b, c)
                else:
                    # 四边形
                    sides = []
                    for i in range(4):
                        sides.append(dist_mm(corners[i], corners[(i+1)%4],
                                             pixel_width))
                    draw_poly(img, corners, color=(255, 0, 255), thickness=2)
                    info = "Q:%.1f/%.1f/%.1f/%.1f mm" % tuple(sides)
                    img.draw_string(int(blob.cx()+x0), int(blob.cy()+y0), info,
                                    color=(255, 0, 255), scale=1)
                    print("Quad sides:", sides)

    # 显示
    Display.show_image(img,
                       x=(LCD_W - img.width())//2,
                       y=(LCD_H - img.height())//2)

